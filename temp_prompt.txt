
            Generate Gherkin scenarios for the feature 'custome2' based on the following acceptance criteria:
            
            - When application is open user can validate in main page have text "Ruang Murid"
- When application is open user can validate in main page have text "Ruang Orang Tua"
            
            Follow these guidelines:
            1. Create separate Scenario or Scenario Outline for each distinct test case
            2. Use clear and descriptive Given/When/Then steps
            3. Include appropriate tags if needed
            4. Follow BDD best practices
            5. Use the professional Gherkin style patterns
            
            Return only the Gherkin scenarios without any additional text or explanations.
            
            
            Examples of professional Gherkin style:
            {'setup_authentication': ['Given I launch the mobile application', 'Given I am on the main screen'], 'navigate_to_screen': ['When I navigate to the {screen_name} screen', 'When I access the {screen_name} feature'], 'verify_response': ['Then the application should respond appropriately', 'Then the interface should be responsive'], 'validate_element': ['Then I should see the {element} element', 'And the {element} should be visible'], 'assert_match': ['Then the {field} should match {expected}', 'And the {field} should be {expected}']}
            

Additional context:
Context for basic_context:
Mobile UI testing context for custome2 feature